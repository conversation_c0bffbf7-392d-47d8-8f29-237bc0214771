// Spotify API 测试脚本
// 运行命令: node test-spotify.js

const fs = require('fs')
const path = require('path')

// 从 .env.example 文件读取配置
function loadEnvExample() {
  try {
    const envPath = path.join(__dirname, '.env.example')
    const envContent = fs.readFileSync(envPath, 'utf8')

    const envVars = {}
    envContent.split('\n').forEach(line => {
      line = line.trim()
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=')
        if (key && valueParts.length > 0) {
          envVars[key] = valueParts.join('=')
        }
      }
    })

    return envVars
  } catch (error) {
    console.error('❌ 无法读取 .env.example 文件:', error.message)
    return {}
  }
}

const envVars = loadEnvExample()
const accessToken = envVars.NEXT_PUBLIC_SPOTIFY_ACCESS_TOKEN

if (!accessToken) {
  console.error('❌ 在 .env.example 中未找到 NEXT_PUBLIC_SPOTIFY_ACCESS_TOKEN')
  process.exit(1)
}

async function testSpotifyAPI() {
  console.log('🎵 开始测试 Spotify API...')
  console.log('Token长度:', accessToken ? accessToken.length : 'undefined')
  console.log('Token前20字符:', accessToken ? accessToken.substring(0, 20) + '...' : 'undefined')
  console.log('完整Authorization头:', `Bearer ${accessToken}`)
  
  try {
    console.log('\n📡 正在调用 Spotify API...')
    
    const response = await fetch('https://api.spotify.com/v1/me/player', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('📊 响应状态:', response.status)
    console.log('📊 响应状态文本:', response.statusText)
    
    if (response.status === 204) {
      console.log('🔇 当前没有播放任何内容')
      return
    }
    
    if (response.status === 401) {
      console.log('❌ Token 无效或已过期')
      console.log('请检查 Spotify token 是否正确')
      return
    }
    
    if (!response.ok) {
      console.log('❌ API 调用失败:', response.status, response.statusText)
      const errorText = await response.text()
      console.log('错误详情:', errorText)
      return
    }
    
    const data = await response.json()
    console.log('\n✅ 成功获取数据!')
    
    if (data.item) {
      console.log('\n🎵 当前播放信息:')
      console.log('歌曲:', data.item.name)
      console.log('艺术家:', data.item.artists.map(artist => artist.name).join(', '))
      console.log('专辑:', data.item.album.name)
      console.log('播放状态:', data.is_playing ? '▶️ 播放中' : '⏸️ 暂停')
      console.log('进度:', Math.round((data.progress_ms / data.item.duration_ms) * 100) + '%')
      console.log('专辑封面:', data.item.album.images[0]?.url || '无')
      console.log('Spotify链接:', data.item.external_urls.spotify)
      
      // 格式化为组件需要的数据格式
      const formattedData = {
        album: data.item.album.name,
        artist: data.item.artists.map(artist => artist.name).join(', '),
        artist_url: data.item.artists[0]?.external_urls.spotify || '',
        artwork_url: data.item.album.images[0]?.url || '',
        artwork_placeholder_url: '',
        client_type: 'spotify',
        play_percent: data.item.duration_ms ? (data.progress_ms / data.item.duration_ms) * 100 : 0,
        play_time: formatTime(data.progress_ms),
        player_state: data.is_playing,
        spotify_url: data.item.external_urls.spotify,
        timestamp: Math.floor(data.timestamp / 1000),
        total_time: formatTime(data.item.duration_ms),
        track_name: data.item.name,
      }
      
      console.log('\n📋 格式化后的数据:')
      console.log(JSON.stringify(formattedData, null, 2))
      
    } else {
      console.log('⚠️ 没有获取到歌曲信息')
    }
    
  } catch (error) {
    console.log('❌ 请求失败:', error.message)
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 可能的解决方案:')
      console.log('1. 检查网络连接')
      console.log('2. 确保 Spotify 应用正在播放音乐')
      console.log('3. 验证 token 是否有效')
      console.log('4. 检查 token 权限范围是否包含 user-read-currently-playing')
    }
  }
}

function formatTime(ms) {
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 运行测试
testSpotifyAPI()

// 也测试一下用户信息API（不需要播放状态）
async function testUserInfo() {
  console.log('\n\n👤 测试用户信息 API...')
  
  try {
    const response = await fetch('https://api.spotify.com/v1/me', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      }
    })
    
    if (response.ok) {
      const userData = await response.json()
      console.log('✅ 用户信息获取成功!')
      console.log('用户名:', userData.display_name)
      console.log('用户ID:', userData.id)
      console.log('国家:', userData.country)
      console.log('订阅类型:', userData.product)
    } else {
      console.log('❌ 用户信息获取失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 用户信息请求失败:', error.message)
  }
}

// 延迟执行用户信息测试
setTimeout(testUserInfo, 2000)
