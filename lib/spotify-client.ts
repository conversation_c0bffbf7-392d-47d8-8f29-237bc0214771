// 客户端Spotify数据获取工具
// 直接使用从 .env.example 复制的配置

// Spotify Access Token (从 .env.example 复制)
const SPOTIFY_ACCESS_TOKEN = 'BQAdIwN66NLsAPOuB1D3BrLYs0QUilzc5uGDDvLnqU8_UH3KyISBfK_bxCk0uk-cZghSgoV6oYdgC1rV74F48XjAJt6S5Eb2GKgZRgfgKBZqp59DcdcevvDKPeqtAI-ArsQSPUBdSPS17UZ4rsvZOrZDOyOGHsidmJT1aGOm42wT3rFBnZFCDihJ2Sf8edNZvqB6GYXnNDyf9WGA5A7cYKttIitNnaBKAFhYj4yfBMq90M4Wp-pHG4BLwNvWHfVtl63i_0NvIoSFdPzGtKp6TdqEBH_K9I_5MkkP1pd-20iO6yESzX16lXlVzhH7tkGWRir8brRgm1i7ayEf9H9tt53qF-svcGiGEwhLhiLCc0K7XT6toaXKxLvMvLm72409C-8h-2xDyMrE'

export interface SpotifyTrackData {
  album: string
  artist: string
  artist_url: string
  artwork_url: string
  artwork_placeholder_url: string
  client_type: string
  play_percent: number
  play_time: string
  player_state: boolean
  spotify_url: string
  timestamp: number
  total_time: string
  track_name: string
}

// 备用数据
export const fallbackSpotifyData: SpotifyTrackData = {
  album: 'Timeless',
  artist: 'Khalil Fong',
  artist_url: '',
  artwork_placeholder_url: '',
  artwork_url: 'https://pub-85fe3948f0644e2cba137d74f3630b8b.r2.dev/IMG_4040.jpeg',
  client_type: 'Apple Music',
  play_percent: 0,
  play_time: '',
  player_state: true,
  spotify_url: '',
  timestamp: 1726282587,
  total_time: '',
  track_name: 'Red Bean',
}

// 格式化时间
export function formatTime(ms: number): string {
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 使用真实Spotify API获取数据
export async function fetchSpotifyData(): Promise<{ data: SpotifyTrackData | null; error: string | null }> {
  try {
    // 获取Spotify access token
    const accessToken = SPOTIFY_ACCESS_TOKEN

    if (!accessToken) {
      console.warn('No Spotify access token found')
      return { data: null, error: 'Spotify access token not configured' }
    }

    const response = await fetch('https://api.spotify.com/v1/me/player', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.status === 204) {
      // 没有播放内容，获取最近播放的歌曲
      console.log('🔄 当前没有播放内容，获取最近播放的歌曲...')

      try {
        const recentResponse = await fetch('https://api.spotify.com/v1/me/player/recently-played?limit=1', {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })

        if (recentResponse.ok) {
          const recentData = await recentResponse.json()

          if (recentData.items && recentData.items.length > 0) {
            const recentTrack = recentData.items[0].track

            // 格式化最近播放的歌曲数据
            const formattedData: SpotifyTrackData = {
              album: recentTrack.album.name,
              artist: recentTrack.artists.map((artist: any) => artist.name).join(', '),
              artist_url: recentTrack.artists[0]?.external_urls.spotify || '',
              artwork_url: recentTrack.album.images[0]?.url || '',
              artwork_placeholder_url: '',
              client_type: 'spotify',
              play_percent: 0, // 最近播放的歌曲没有进度
              play_time: '0:00',
              player_state: false, // 不在播放状态
              spotify_url: recentTrack.external_urls.spotify,
              timestamp: Math.floor(Date.now() / 1000),
              total_time: formatTime(recentTrack.duration_ms),
              track_name: recentTrack.name,
            }

            return { data: formattedData, error: null }
          }
        }
      } catch (recentError) {
        console.warn('获取最近播放歌曲失败:', recentError)
      }

      // 如果获取最近播放也失败，返回错误
      return { data: null, error: 'Nothing is currently playing and failed to get recent tracks' }
    }

    if (!response.ok) {
      if (response.status === 401) {
        return { data: null, error: 'Spotify token expired or lacks required permissions (user-read-currently-playing)' }
      }
      if (response.status === 403) {
        return { data: null, error: 'Spotify token lacks required permissions' }
      }
      throw new Error(`Spotify API error: ${response.status}`)
    }

    const spotifyData = await response.json()

    if (!spotifyData.item) {
      return { data: null, error: 'No track information available' }
    }

    // 格式化Spotify数据
    const formattedData: SpotifyTrackData = {
      album: spotifyData.item.album.name,
      artist: spotifyData.item.artists.map((artist: any) => artist.name).join(', '),
      artist_url: spotifyData.item.artists[0]?.external_urls.spotify || '',
      artwork_url: spotifyData.item.album.images[0]?.url || '',
      artwork_placeholder_url: '',
      client_type: 'spotify',
      play_percent: spotifyData.item.duration_ms ? (spotifyData.progress_ms / spotifyData.item.duration_ms) * 100 : 0,
      play_time: formatTime(spotifyData.progress_ms),
      player_state: spotifyData.is_playing,
      spotify_url: spotifyData.item.external_urls.spotify,
      timestamp: Math.floor(spotifyData.timestamp / 1000),
      total_time: formatTime(spotifyData.item.duration_ms),
      track_name: spotifyData.item.name,
    }

    return { data: formattedData, error: null }

  } catch (error) {
    console.error('Error fetching Spotify data:', error)
    return { data: null, error: 'Failed to fetch Spotify data' }
  }
}
